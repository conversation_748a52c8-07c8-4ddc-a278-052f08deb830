# VTravels - Full Stack Travel Application

A comprehensive travel booking platform built with modern technologies.

## Architecture Overview

### Backend Services
- **Python API Server** (FastAPI) - Main backend for user management, bookings, payments
- **Node.js Microservices** - Travel search, recommendations, notifications, reviews

### Databases
- **MySQL** - User accounts, bookings, payments, transactions
- **MongoDB** - Travel destinations, hotels, flights, reviews, search data

### Frontend
- **React Web App** - Responsive travel booking interface

### Infrastructure
- **API Gateway** - Request routing and load balancing
- **Docker** - Containerized deployment
- **Redis** - Caching and session management

## Project Structure

```
vtravels/
├── backend/                    # Python FastAPI backend
│   ├── app/
│   ├── models/
│   ├── routes/
│   ├── services/
│   └── requirements.txt
├── microservices/             # Node.js microservices
│   ├── travel-search/
│   ├── recommendations/
│   ├── notifications/
│   └── reviews/
├── frontend/                  # React web application
│   ├── src/
│   ├── public/
│   └── package.json
├── databases/                 # Database schemas and migrations
│   ├── mysql/
│   └── mongodb/
├── api-gateway/              # API Gateway configuration
├── docker/                   # Docker configurations
└── docs/                     # Documentation
```

## Features

### Core Features
- User registration and authentication
- Travel search (flights, hotels, packages)
- Booking management
- Payment processing
- User reviews and ratings
- Travel recommendations
- Real-time notifications

### Advanced Features
- Multi-currency support
- Travel itinerary planning
- Social features (trip sharing)
- Mobile-responsive design
- Admin dashboard
- Analytics and reporting

## Technology Stack

### Backend
- **Python 3.11+** with FastAPI
- **SQLAlchemy** for MySQL ORM
- **PyMongo** for MongoDB
- **JWT** for authentication
- **Celery** for background tasks

### Microservices
- **Node.js 18+** with Express
- **Mongoose** for MongoDB
- **Redis** for caching
- **Socket.io** for real-time features

### Frontend
- **React 18** with TypeScript
- **Material-UI** for components
- **Redux Toolkit** for state management
- **React Query** for API calls

### DevOps
- **Docker** and Docker Compose
- **Nginx** as reverse proxy
- **GitHub Actions** for CI/CD

## Getting Started

### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker and Docker Compose
- MySQL 8.0+
- MongoDB 6.0+
- Redis 7.0+

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd vtravels

# Start all services with Docker
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Python API: http://localhost:8000
# API Gateway: http://localhost:8080
```

## Development Setup

### Backend Development
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000
```

### Microservices Development
```bash
cd microservices/travel-search
npm install
npm run dev
```

### Frontend Development
```bash
cd frontend
npm install
npm start
```

## API Documentation

- Python API: http://localhost:8000/docs (Swagger UI)
- Microservices APIs: Available at respective service ports

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
